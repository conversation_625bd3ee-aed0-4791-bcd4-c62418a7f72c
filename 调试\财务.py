# 导入必要的库
import pandas as pd
import tushare as ts
import time
from datetime import datetime
import os
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import queue
import random

# 初始化pro接口
pro = ts.pro_api('2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')

# 全局变量
progress_lock = threading.Lock()
progress_counter = {'processed': 0, 'success': 0, 'failed': 0, 'saved': 0}

# 创建输出文件夹
base_output_dir = "stock-fin-data-xbx"
if not os.path.exists(base_output_dir):
    os.makedirs(base_output_dir)
    print(f"创建基础文件夹: {base_output_dir}")

# 公司类型映射
COMP_TYPE_NAMES = {
    '1': '一般企业',
    '2': '银行',
    '3': '保险',
    '4': '证券'
}

def get_stock_list():
    """获取股票列表"""
    print("获取股票列表...")
    try:
        stock_list = pro.stock_basic(exchange='', 
                                   list_status='L', 
                                   fields='ts_code,symbol,name,area,industry,market')
        print(f"获取到 {len(stock_list)} 只股票")
        return stock_list
    except Exception as e:
        print(f"获取股票列表失败: {e}")
        return pd.DataFrame()

def clean_filename(filename):
    """清理文件名中的非法字符"""
    invalid_chars = ['<', '>', ':', '"', '/', '\\', '|', '?', '*']
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    return filename

def convert_ts_code_to_filename_format(ts_code):
    """
    将股票代码转换为文件名格式
    例: 000001.SZ -> sz000001, 430047.BJ -> bj430047, 600000.SH -> sh600000
    """
    if '.' in ts_code:
        code, exchange = ts_code.split('.')
        if exchange == 'SZ':
            return f"sz{code}"
        elif exchange == 'SH':
            return f"sh{code}"
        elif exchange == 'BJ':
            return f"bj{code}"
        else:
            return f"{exchange.lower()}{code}"
    else:
        return ts_code.lower()

def process_financial_data(df_balance, df_income, df_cashflow, ts_code):
    """处理和合并财务数据，按照新的字段要求"""

    # 字段映射字典
    field_mapping = {
        # 必须字段
        'ts_code': 'stock_code',
        'end_date': 'report_date',
        'f_ann_date': 'publish_date',

        # 利润表字段
        'n_income': 'R_np@xbx',  # 净利润
        'n_income_attr_p': 'R_np_atoopc@xbx',  # 归属于母公司所有者的净利润
        'revenue': 'R_revenue@xbx',  # 营业收入
        'operate_profit': 'R_op@xbx',  # 营业利润

        # 资产负债表字段
        'total_hldr_eqy_exc_min_int': 'B_total_equity_atoopc@xbx',  # 归属于母公司所有者权益合计
        'total_liab': 'B_total_liab@xbx',  # 负债合计
        'paid_in_capital': 'B_actual_received_capital@xbx',  # 实收资本
        'preferred_shares': 'B_preferred_shares@xbx',  # 优先股
        'total_assets': 'B_total_assets@xbx',  # 资产总计
        'total_liab_hldr_eqy': 'B_total_liab_and_owner_equity@xbx',  # 负债和所有者权益总计
        'total_cur_liab': 'B_total_current_liab@xbx',  # 流动负债合计
        'total_ncur_liab': 'B_total_noncurrent_liab@xbx',  # 非流动负债合计

        # 现金流量表字段
        'n_cashflow_act': 'C_ncf_from_oa@xbx'  # 经营活动产生的现金流量净额
    }

    # 合并数据
    merged_data = []

    # 获取所有唯一的报告期
    all_periods = set()
    if not df_balance.empty and 'end_date' in df_balance.columns:
        all_periods.update(df_balance['end_date'].dropna())
    if not df_income.empty and 'end_date' in df_income.columns:
        all_periods.update(df_income['end_date'].dropna())
    if not df_cashflow.empty and 'end_date' in df_cashflow.columns:
        all_periods.update(df_cashflow['end_date'].dropna())

    for period in sorted(all_periods):
        # 为每个报告期创建一行数据
        row_data = {}

        # 基础信息
        row_data['stock_code'] = convert_ts_code_to_filename_format(ts_code)
        row_data['report_date'] = period

        # 从资产负债表获取发布日期
        balance_row = df_balance[df_balance['end_date'] == period] if not df_balance.empty else pd.DataFrame()
        if not balance_row.empty:
            row_data['publish_date'] = balance_row.iloc[0].get('f_ann_date', '')
        else:
            # 从利润表获取发布日期
            income_row = df_income[df_income['end_date'] == period] if not df_income.empty else pd.DataFrame()
            if not income_row.empty:
                row_data['publish_date'] = income_row.iloc[0].get('f_ann_date', '')
            else:
                row_data['publish_date'] = ''

        # 从各个表中获取数据
        for source_field, target_field in field_mapping.items():
            if source_field in ['ts_code', 'end_date', 'f_ann_date']:
                continue  # 已处理

            value = None

            # 从资产负债表获取
            if not df_balance.empty and source_field in df_balance.columns:
                balance_row = df_balance[df_balance['end_date'] == period]
                if not balance_row.empty:
                    value = balance_row.iloc[0].get(source_field)

            # 从利润表获取
            if value is None and not df_income.empty and source_field in df_income.columns:
                income_row = df_income[df_income['end_date'] == period]
                if not income_row.empty:
                    value = income_row.iloc[0].get(source_field)

            # 从现金流量表获取
            if value is None and not df_cashflow.empty and source_field in df_cashflow.columns:
                cashflow_row = df_cashflow[df_cashflow['end_date'] == period]
                if not cashflow_row.empty:
                    value = cashflow_row.iloc[0].get(source_field)

            # 设置值，如果为空则设为None
            row_data[target_field] = value if pd.notna(value) else None

        merged_data.append(row_data)

    # 创建DataFrame
    if merged_data:
        df_result = pd.DataFrame(merged_data)

        # 按报告期排序
        if 'report_date' in df_result.columns:
            df_result = df_result.sort_values('report_date', ascending=True)

        return df_result.reset_index(drop=True)
    else:
        return pd.DataFrame()

def save_stock_data(ts_code, stock_name, df_data, comp_type):
    """保存单只股票数据到CSV文件，使用新的两层文件夹结构和GBK编码"""
    if df_data.empty:
        return False

    try:
        # 转换股票代码为文件名格式
        stock_code_format = convert_ts_code_to_filename_format(ts_code)

        # 获取公司类型名称
        comp_type_name = COMP_TYPE_NAMES.get(comp_type, '一般企业')

        # 创建两层文件夹结构
        stock_folder = os.path.join(base_output_dir, stock_code_format)
        if not os.path.exists(stock_folder):
            os.makedirs(stock_folder)

        # CSV文件命名格式：股票代码_公司类型.csv
        filename = f"{stock_code_format}_{comp_type_name}.csv"
        filepath = os.path.join(stock_folder, filename)

        # 创建正确的格式：第一行空行，第二行列名，然后是数据
        # 先保存数据部分（不包含列名）
        df_data.to_csv(filepath, index=False, encoding='gbk', header=False)

        # 读取刚保存的文件
        with open(filepath, 'r', encoding='gbk') as f:
            data_lines = f.readlines()

        # 重新写入文件，按正确格式：空行 + 列名 + 数据
        with open(filepath, 'w', encoding='gbk') as f:
            # 第一行：空行（用逗号分隔的空值）
            f.write(',' * (len(df_data.columns) - 1) + '\n')
            # 第二行：列名
            f.write(','.join(df_data.columns) + '\n')
            # 第三行开始：数据
            for line in data_lines:
                f.write(line)

        with progress_lock:
            progress_counter['saved'] += 1

        return True
    except Exception as e:
        print(f"保存 {ts_code} 数据失败: {e}")
        return False

def get_financial_data_for_stock(stock_info, max_retries=3):
    """
    获取单只股票的财务数据并保存
    """
    ts_code = stock_info['ts_code']
    stock_name = stock_info['name']
    industry = stock_info.get('industry', '')
    
    # 根据行业自动判断公司类型
    if '银行' in industry:
        comp_type = '2'  # 银行
    elif '保险' in industry:
        comp_type = '3'  # 保险
    elif '证券' in industry or '券商' in industry:
        comp_type = '4'  # 证券
    else:
        comp_type = '1'  # 一般工商业
    
    # 随机延时，避免API限制
    time.sleep(random.uniform(0.1, 0.5))
    
    for attempt in range(max_retries):
        try:
            # 1. 获取资产负债表数据
            df_balance = pro.balancesheet(**{
                "ts_code": ts_code,
                "ann_date": "",
                "f_ann_date": "",
                "start_date": "",
                "end_date": "",
                "period": "",
                "report_type": "",
                "comp_type": comp_type,
                "limit": "",
                "offset": ""
            }, fields=[
                "ts_code",
                "end_date", 
                "f_ann_date",
                "total_assets",      # 资产总计
                "total_liab",        # 负债合计
                "total_hldr_eqy_exc_min_int",  # 归属于母公司所有者权益合计
                "total_ncl",         # 非流动负债合计
                "total_cur_liab",    # 流动负债合计
                "total_liab_hldr_eqy",  # 负债和所有者权益总计
                "accounts_receiv_bill",    # 应收账款
                "oth_eqt_tools_p_shr"  # 其他权益工具-优先股
            ])
            
            time.sleep(random.uniform(0.2, 0.4))  # API调用间隔
            
            # 2. 获取利润表数据
            df_income = pro.income(**{
                "ts_code": ts_code,
                "ann_date": "",
                "f_ann_date": "",
                "start_date": "",
                "end_date": "",
                "period": "",
                "report_type": "",
                "comp_type": comp_type,
                "is_calc": "",
                "limit": "",
                "offset": ""
            }, fields=[
                "ts_code",
                "end_date",
                "f_ann_date",
                "n_income_attr_p",   # 归母净利润
                "n_income",          # 净利润
                "revenue",           # 营业收入
                "operate_profit",    # 营业利润
                "comp_type",
                "report_type"
            ])
            
            time.sleep(random.uniform(0.2, 0.4))  # API调用间隔
            
            # 3. 获取现金流量表数据
            df_cashflow = pro.cashflow(**{
                "ts_code": ts_code,
                "ann_date": "",
                "f_ann_date": "",
                "start_date": "",
                "end_date": "",
                "period": "",
                "report_type": "",
                "comp_type": comp_type,
                "limit": "",
                "offset": ""
            }, fields=[
                "ts_code",
                "end_date",
                "f_ann_date", 
                "n_cashflow_act"    # 经营活动现金流量净额
            ])
            
            # 使用新的数据处理逻辑
            df_processed = process_financial_data(df_balance, df_income, df_cashflow, ts_code)

            if not df_processed.empty:

                # 保存到文件
                save_success = save_stock_data(ts_code, stock_name, df_processed, comp_type)
                
                # 更新进度
                with progress_lock:
                    progress_counter['processed'] += 1
                    progress_counter['success'] += 1
                    if progress_counter['processed'] % 50 == 0:
                        print(f"进度: {progress_counter['processed']} 已处理, "
                              f"{progress_counter['success']} 成功, "
                              f"{progress_counter['failed']} 失败, "
                              f"{progress_counter['saved']} 已保存")
                
                return {
                    'status': 'success',
                    'ts_code': ts_code,
                    'stock_name': stock_name,
                    'records': len(df_processed),
                    'saved': save_success
                }
            else:
                with progress_lock:
                    progress_counter['processed'] += 1
                    progress_counter['failed'] += 1
                
                return {
                    'status': 'no_data',
                    'ts_code': ts_code,
                    'stock_name': stock_name,
                    'records': 0,
                    'saved': False
                }
                
        except Exception as e:
            if attempt < max_retries - 1:
                wait_time = random.uniform(1, 3) * (attempt + 1)
                time.sleep(wait_time)
            else:
                with progress_lock:
                    progress_counter['processed'] += 1
                    progress_counter['failed'] += 1
                
                return {
                    'status': 'error',
                    'ts_code': ts_code,
                    'stock_name': stock_name,
                    'error': str(e),
                    'records': 0,
                    'saved': False
                }
    
    return {
        'status': 'error',
        'ts_code': ts_code,
        'stock_name': stock_name,
        'error': 'Max retries exceeded',
        'records': 0,
        'saved': False
    }

def create_summary_report(stock_list, results_summary):
    """创建汇总报告"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = os.path.join(base_output_dir, f"下载汇总报告_{timestamp}.txt")
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("股票财务数据下载汇总报告\n")
            f.write("=" * 60 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # 总体统计
            total_stocks = len(stock_list)
            f.write("总体统计:\n")
            f.write(f"  总股票数: {total_stocks}\n")
            f.write(f"  处理完成: {progress_counter['processed']}\n")
            f.write(f"  成功获取: {progress_counter['success']}\n")
            f.write(f"  成功保存: {progress_counter['saved']}\n")
            f.write(f"  获取失败: {progress_counter['failed']}\n")
            f.write(f"  成功率: {progress_counter['success']/total_stocks*100:.2f}%\n\n")
            
            # 按行业统计
            if results_summary:
                industry_stats = {}
                for result in results_summary:
                    if result['status'] == 'success':
                        # 从股票列表中获取行业信息
                        stock_info = stock_list[stock_list['ts_code'] == result['ts_code']]
                        if not stock_info.empty:
                            industry = stock_info.iloc[0]['industry']
                            if industry not in industry_stats:
                                industry_stats[industry] = {'success': 0, 'total': 0}
                            industry_stats[industry]['success'] += 1
                    
                    # 统计总数
                    stock_info = stock_list[stock_list['ts_code'] == result['ts_code']]
                    if not stock_info.empty:
                        industry = stock_info.iloc[0]['industry']
                        if industry not in industry_stats:
                            industry_stats[industry] = {'success': 0, 'total': 0}
                        industry_stats[industry]['total'] += 1
                
                f.write("按行业统计:\n")
                for industry, stats in sorted(industry_stats.items()):
                    success_rate = stats['success']/stats['total']*100 if stats['total'] > 0 else 0
                    f.write(f"  {industry}: {stats['success']}/{stats['total']} ({success_rate:.1f}%)\n")
                f.write("\n")
            
            # 失败股票列表
            failed_stocks = [r for r in results_summary if r['status'] != 'success']
            if failed_stocks:
                f.write(f"失败股票列表 ({len(failed_stocks)} 只):\n")
                for result in failed_stocks:
                    reason = result.get('error', '无数据')
                    f.write(f"  {result['ts_code']} - {result['stock_name']}: {reason}\n")
        
        print(f"汇总报告已保存: {report_file}")
        return report_file
    
    except Exception as e:
        print(f"创建汇总报告失败: {e}")
        return None

def main():
    """
    主函数：使用多线程获取所有股票的财务数据，每个股票保存为独立CSV
    """
    print("=" * 80)
    print("开始多线程下载股票财务数据 - 每股票独立文件模式")
    print("=" * 80)
    
    start_time = time.time()
    
    # 1. 获取股票列表
    stock_list = get_stock_list()
    if stock_list.empty:
        print("未能获取股票列表，程序退出")
        return
    
    # 可选：限制股票数量进行测试
    # stock_list = stock_list.head(100)  # 取消注释以测试前100只股票
    
    total_stocks = len(stock_list)
    print(f"\n准备处理 {total_stocks} 只股票")
    print(f"数据将保存到文件夹: {base_output_dir}")
    
    # 2. 配置多线程参数
    max_workers = 8   # 适当减少线程数，避免文件I/O冲突
    batch_size = 200  # 批次大小
    
    print(f"使用 {max_workers} 个线程并发处理")
    print(f"批次大小: {batch_size}")
    
    # 3. 记录所有结果用于汇总
    all_results = []
    
    # 4. 分批处理股票
    for batch_start in range(0, total_stocks, batch_size):
        batch_end = min(batch_start + batch_size, total_stocks)
        batch_stocks = stock_list.iloc[batch_start:batch_end]
        
        print(f"\n处理第 {batch_start//batch_size + 1} 批: "
              f"股票 {batch_start+1}-{batch_end} / {total_stocks}")
        
        # 创建线程池
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交任务
            futures = []
            for _, stock in batch_stocks.iterrows():
                stock_info = {
                    'ts_code': stock['ts_code'],
                    'name': stock['name'],
                    'area': stock.get('area', ''),
                    'industry': stock.get('industry', ''),
                    'market': stock.get('market', '')
                }
                future = executor.submit(get_financial_data_for_stock, stock_info)
                futures.append(future)
            
            # 处理结果
            batch_success = 0
            batch_results = []
            
            for future in as_completed(futures):
                try:
                    result = future.result(timeout=120)  # 2分钟超时
                    batch_results.append(result)
                    
                    if result['status'] == 'success':
                        batch_success += 1
                    elif result['status'] == 'no_data':
                        print(f"  {result['ts_code']}: 无数据")
                    else:
                        print(f"  {result['ts_code']}: 失败 - {result.get('error', 'Unknown error')}")
                        
                except Exception as e:
                    print(f"处理任务结果时出错: {e}")
            
            # 记录批次结果
            all_results.extend(batch_results)
        
        print(f"第 {batch_start//batch_size + 1} 批完成: 成功 {batch_success}/{len(batch_stocks)} 只股票")
        print(f"累计处理: {progress_counter['processed']}/{total_stocks}")
        
        # 批次间暂停，避免API限制
        if batch_end < total_stocks:
            print("批次间暂停 30 秒...")
            time.sleep(30)
    
    # 5. 生成最终统计报告
    print(f"\n数据下载完成!")
    print(f"总处理股票: {progress_counter['processed']}")
    print(f"成功获取数据: {progress_counter['success']}")
    print(f"成功保存文件: {progress_counter['saved']}")
    print(f"处理失败: {progress_counter['failed']}")
    print(f"成功率: {progress_counter['success']/total_stocks*100:.2f}%")
    
    # 检查文件夹中的文件数量
    saved_files = []
    total_size = 0

    # 遍历两层文件夹结构统计文件
    if os.path.exists(base_output_dir):
        for stock_folder in os.listdir(base_output_dir):
            stock_folder_path = os.path.join(base_output_dir, stock_folder)
            if os.path.isdir(stock_folder_path):
                for filename in os.listdir(stock_folder_path):
                    if filename.endswith('.csv'):
                        saved_files.append(filename)
                        filepath = os.path.join(stock_folder_path, filename)
                        total_size += os.path.getsize(filepath)

    print(f"文件夹中CSV文件数量: {len(saved_files)}")
    
    print(f"文件夹总大小: {total_size/(1024*1024):.2f} MB")
    
    # 创建汇总报告
    create_summary_report(stock_list, all_results)
    
    # 显示部分文件示例
    if saved_files:
        print(f"\n📁 保存的文件示例:")
        for filename in saved_files[:5]:  # 显示前5个文件
            print(f"  {filename}")
        if len(saved_files) > 5:
            print(f"  ... 还有 {len(saved_files)-5} 个文件")

    # 计算总耗时
    total_time = time.time() - start_time
    print(f"\n⏱️  总耗时: {total_time/3600:.2f} 小时 ({total_time/60:.1f} 分钟)")
    print(f"📂 所有数据已保存到文件夹: {base_output_dir}")
    print("🎉 程序执行完成!")
    print("=" * 80)

# 执行主函数
if __name__ == "__main__":
    main()